"use client";

import { FC, useEffect, useCallback } from 'react';
import { AssistantRuntimeProvider } from "@assistant-ui/react";
import { useChatRuntime } from "@assistant-ui/react-ai-sdk";
import { TooltipProvider } from "@/components/ui/tooltip";
import { RealtimeThread } from '@/components/assistant-ui/realtime-thread';
import { ControlPanel } from '@/components/assistant-ui/control-panel';
import { useRealtimeInterview } from '@/hooks/useRealtimeInterview';
import { VoiceOption } from '@/lib/session-config';

interface RealtimeInterviewAppProps {
  apiKey: string;
}

export const RealtimeInterviewApp: FC<RealtimeInterviewAppProps> = ({ apiKey }) => {
  // Set up the chat runtime for assistant-ui
  const runtime = useChatRuntime();

  const {
    connectionState,
    isConnected,
    isRecording,
    isPlaying,
    isMuted,
    playbackRate,
    sessionMetrics,
    isSessionExpired,
    shouldShowWarning,
    connect,
    disconnect,
    startRecording,
    stopRecording,
    triggerResponse,
    setMuted,
    setPlaybackRate,
    error
  } = useRealtimeInterview({
    apiKey,
    sessionConfig: {
      voice: 'alloy',
      enableTranscription: true,
      interviewType: 'system-design'
    },
    autoConnect: false,
    onError: (error) => {
      console.error('Interview session error:', error);
    },
    onSessionExpired: () => {
      console.log('Session expired, disconnecting...');
    },
    onWarning: (message) => {
      console.warn('Session warning:', message);
    }
  });

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ignore if user is typing in an input field
      if (event.target instanceof HTMLInputElement || 
          event.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (event.key.toLowerCase()) {
        case ' ':
          event.preventDefault();
          if (isConnected) {
            triggerResponse();
          }
          break;
        case 'm':
          event.preventDefault();
          if (isConnected) {
            setMuted(!isMuted);
          }
          break;
        case 'r':
          event.preventDefault();
          if (isConnected) {
            if (isRecording) {
              stopRecording();
            } else {
              startRecording();
            }
          }
          break;
        case 'escape':
          event.preventDefault();
          if (isConnected) {
            disconnect();
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isConnected, isRecording, isMuted, triggerResponse, setMuted, startRecording, stopRecording, disconnect]);

  const handleToggleRecording = useCallback(() => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [isRecording, startRecording, stopRecording]);

  const handleVoiceChange = useCallback((voice: VoiceOption) => {
    // Voice change would require reconnection with new session config
    // For now, we'll just log it - in a full implementation, you'd want to
    // disconnect, update the session config, and reconnect
    console.log('Voice change requested:', voice);
  }, []);

  // Note: In a complete implementation, you'd integrate the WebSocket events with assistant-ui
  // by creating a proper AssistantRuntime that connects to your realtime WebSocket events

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <TooltipProvider>
        <div className="h-screen flex flex-col bg-gray-50">
        {/* Header */}
        <header className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Realtime Interview Assistant
              </h1>
              <p className="text-sm text-gray-600">
                Practice system design interviews with AI feedback
              </p>
            </div>
            
            {/* Status indicators */}
            <div className="flex items-center gap-4">
              {shouldShowWarning && (
                <div className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">
                  Session expires soon
                </div>
              )}
              
              {isSessionExpired && (
                <div className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">
                  Session expired
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Chat Interface */}
          <div className="flex-1 overflow-hidden">
            {/* For now, we'll use a simplified version without full AssistantRuntimeProvider */}
            {/* In a complete implementation, you'd integrate the WebSocket events with assistant-ui */}
            <div className="h-full p-6">
              <RealtimeThread
                isRecording={isRecording}
                isMuted={isMuted}
                isPlaying={isPlaying}
                playbackRate={playbackRate}
                onToggleRecording={handleToggleRecording}
                onToggleMute={() => setMuted(!isMuted)}
                onTriggerResponse={triggerResponse}
                onPlaybackRateChange={setPlaybackRate}
                connectionStatus={
                  isConnected ? 'connected' : 
                  connectionState === 'connecting' ? 'connecting' :
                  connectionState === 'error' ? 'error' : 'disconnected'
                }
              />
            </div>
          </div>

          {/* Control Panel */}
          <ControlPanel
            connectionState={connectionState}
            isConnected={isConnected}
            isRecording={isRecording}
            isPlaying={isPlaying}
            isMuted={isMuted}
            playbackRate={playbackRate}
            sessionMetrics={sessionMetrics}
            isSessionExpired={isSessionExpired}
            shouldShowWarning={shouldShowWarning}
            onConnect={connect}
            onDisconnect={disconnect}
            onToggleRecording={handleToggleRecording}
            onTriggerResponse={triggerResponse}
            onToggleMute={() => setMuted(!isMuted)}
            onPlaybackRateChange={setPlaybackRate}
            onVoiceChange={handleVoiceChange}
            error={error}
          />
        </div>

        {/* Instructions Footer */}
        <footer className="bg-gray-100 border-t border-gray-200 px-6 py-3">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-6">
              <span><kbd className="bg-white px-2 py-1 rounded border">Space</kbd> Trigger AI Response</span>
              <span><kbd className="bg-white px-2 py-1 rounded border">M</kbd> Toggle Mute</span>
              <span><kbd className="bg-white px-2 py-1 rounded border">R</kbd> Toggle Recording</span>
            </div>
            <div>
              <span>OpenAI Realtime API • System Design Interview Practice</span>
            </div>
          </div>
        </footer>
        </div>
      </TooltipProvider>
    </AssistantRuntimeProvider>
  );
};
