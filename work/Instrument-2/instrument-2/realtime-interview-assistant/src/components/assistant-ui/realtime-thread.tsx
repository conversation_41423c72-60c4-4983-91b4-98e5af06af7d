"use client";

import {
  ActionBarPrimitive,
  BranchPickerPrimitive,
  ComposerPrimitive,
  MessagePrimitive,
  ThreadPrimitive,
} from "@assistant-ui/react";
import type { FC } from "react";
import {
  ArrowDownIcon,
  CheckIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CopyIcon,
  PencilIcon,
  RefreshCwIcon,
  SendHorizontalIcon,
  MicIcon,
  MicOffIcon,
  PlayIcon,
  VolumeXIcon,
  Volume2Icon,
} from "lucide-react";
import { cn } from "@/lib/utils";

import { Button } from "@/components/ui/button";
import { MarkdownText } from "@/components/assistant-ui/markdown-text";
import { TooltipIconButton } from "@/components/assistant-ui/tooltip-icon-button";

interface RealtimeThreadProps {
  isRecording?: boolean;
  isMuted?: boolean;
  isPlaying?: boolean;
  playbackRate?: number;
  onToggleRecording?: () => void;
  onToggleMute?: () => void;
  onTriggerResponse?: () => void;
  onPlaybackRateChange?: (rate: number) => void;
  connectionStatus?: 'connected' | 'connecting' | 'disconnected' | 'error';
}

export const RealtimeThread: FC<RealtimeThreadProps> = ({
  isRecording = false,
  isMuted = false,
  isPlaying = false,
  playbackRate = 1.0,
  onToggleRecording,
  onToggleMute,
  onTriggerResponse,
  onPlaybackRateChange,
  connectionStatus = 'disconnected'
}) => {
  return (
    <ThreadPrimitive.Root
      className="aui-root aui-thread-root"
      style={{
        ["--thread-max-width" as string]: "48rem",
      }}
    >
      <ThreadPrimitive.Viewport className="aui-thread-viewport">
        <RealtimeWelcome connectionStatus={connectionStatus} />

        <ThreadPrimitive.Messages
          components={{
            UserMessage: UserMessage,
            EditComposer: EditComposer,
            AssistantMessage: AssistantMessage,
          }}
        />

        <ThreadPrimitive.If empty={false}>
          <div className="aui-thread-viewport-spacer" />
        </ThreadPrimitive.If>

        <div className="aui-thread-viewport-footer">
          <ThreadScrollToBottom />
          <RealtimeControls
            isRecording={isRecording}
            isMuted={isMuted}
            isPlaying={isPlaying}
            playbackRate={playbackRate}
            onToggleRecording={onToggleRecording}
            onToggleMute={onToggleMute}
            onTriggerResponse={onTriggerResponse}
            onPlaybackRateChange={onPlaybackRateChange}
          />
          <Composer />
        </div>
      </ThreadPrimitive.Viewport>
    </ThreadPrimitive.Root>
  );
};

const ThreadScrollToBottom: FC = () => {
  return (
    <ThreadPrimitive.ScrollToBottom asChild>
      <TooltipIconButton
        tooltip="Scroll to bottom"
        variant="outline"
        className="aui-thread-scroll-to-bottom"
      >
        <ArrowDownIcon />
      </TooltipIconButton>
    </ThreadPrimitive.ScrollToBottom>
  );
};

const RealtimeWelcome: FC<{ connectionStatus: string }> = ({ connectionStatus }) => {
  const getStatusMessage = () => {
    switch (connectionStatus) {
      case 'connected':
        return "Ready for your mock system design interview! Press the microphone to start recording, then press the trigger button to get AI feedback.";
      case 'connecting':
        return "Connecting to OpenAI Realtime API...";
      case 'error':
        return "Connection error. Please check your API key and try again.";
      default:
        return "Click connect to start your interview session.";
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return "text-green-600";
      case 'connecting':
        return "text-yellow-600";
      case 'error':
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <ThreadPrimitive.Empty>
      <div className="aui-thread-welcome-root">
        <div className="aui-thread-welcome-center">
          <h1 className="text-2xl font-bold mb-4">Realtime Interview Assistant</h1>
          <p className={cn("aui-thread-welcome-message", getStatusColor())}>
            {getStatusMessage()}
          </p>
        </div>
        <RealtimeWelcomeSuggestions />
      </div>
    </ThreadPrimitive.Empty>
  );
};

const RealtimeWelcomeSuggestions: FC = () => {
  return (
    <div className="aui-thread-welcome-suggestions">
      <ThreadPrimitive.Suggestion
        className="aui-thread-welcome-suggestion"
        prompt="I'm ready to start the system design interview. Please begin with a question."
        method="replace"
        autoSend
      >
        <span className="aui-thread-welcome-suggestion-text">
          Start Interview
        </span>
      </ThreadPrimitive.Suggestion>
      <ThreadPrimitive.Suggestion
        className="aui-thread-welcome-suggestion"
        prompt="Can you explain how this realtime interview system works?"
        method="replace"
        autoSend
      >
        <span className="aui-thread-welcome-suggestion-text">
          How does this work?
        </span>
      </ThreadPrimitive.Suggestion>
    </div>
  );
};

interface RealtimeControlsProps {
  isRecording: boolean;
  isMuted: boolean;
  isPlaying: boolean;
  playbackRate: number;
  onToggleRecording?: () => void;
  onToggleMute?: () => void;
  onTriggerResponse?: () => void;
  onPlaybackRateChange?: (rate: number) => void;
}

const RealtimeControls: FC<RealtimeControlsProps> = ({
  isRecording,
  isMuted,
  isPlaying,
  playbackRate,
  onToggleRecording,
  onToggleMute,
  onTriggerResponse,
  onPlaybackRateChange
}) => {
  const playbackRates = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

  return (
    <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg border">
      {/* Recording Control */}
      <TooltipIconButton
        tooltip={isRecording ? "Stop Recording" : "Start Recording"}
        onClick={onToggleRecording}
        className={cn(
          "transition-colors",
          isRecording ? "bg-red-100 text-red-600 hover:bg-red-200" : "hover:bg-gray-200"
        )}
      >
        {isRecording ? <MicOffIcon /> : <MicIcon />}
      </TooltipIconButton>

      {/* Trigger Response */}
      <TooltipIconButton
        tooltip="Trigger AI Response (Spacebar)"
        onClick={onTriggerResponse}
        className="bg-blue-100 text-blue-600 hover:bg-blue-200"
      >
        <SendHorizontalIcon />
      </TooltipIconButton>

      <div className="w-px h-6 bg-gray-300" />

      {/* Mute Control */}
      <TooltipIconButton
        tooltip={isMuted ? "Unmute" : "Mute"}
        onClick={onToggleMute}
        className={cn(
          "transition-colors",
          isMuted ? "bg-red-100 text-red-600 hover:bg-red-200" : "hover:bg-gray-200"
        )}
      >
        {isMuted ? <VolumeXIcon /> : <Volume2Icon />}
      </TooltipIconButton>

      {/* Playback Rate */}
      <div className="flex items-center gap-1">
        <span className="text-sm text-gray-600">Speed:</span>
        <select
          value={playbackRate}
          onChange={(e) => onPlaybackRateChange?.(parseFloat(e.target.value))}
          className="text-sm border rounded px-2 py-1 bg-white"
        >
          {playbackRates.map(rate => (
            <option key={rate} value={rate}>
              {rate}x
            </option>
          ))}
        </select>
      </div>

      {/* Playing Indicator */}
      {isPlaying && (
        <div className="flex items-center gap-1 text-green-600">
          <PlayIcon className="w-4 h-4" />
          <span className="text-sm">Playing</span>
        </div>
      )}
    </div>
  );
};

const Composer: FC = () => {
  return (
    <ComposerPrimitive.Root className="aui-composer-root">
      <ComposerPrimitive.Input
        rows={1}
        autoFocus
        placeholder="Type a message or use voice controls..."
        className="aui-composer-input"
      />
      <ComposerAction />
    </ComposerPrimitive.Root>
  );
};

const ComposerAction: FC = () => {
  return (
    <>
      <ThreadPrimitive.If running={false}>
        <ComposerPrimitive.Send asChild>
          <TooltipIconButton
            tooltip="Send"
            variant="default"
            className="aui-composer-send"
          >
            <SendHorizontalIcon />
          </TooltipIconButton>
        </ComposerPrimitive.Send>
      </ThreadPrimitive.If>
      <ThreadPrimitive.If running>
        <ComposerPrimitive.Cancel asChild>
          <TooltipIconButton
            tooltip="Cancel"
            variant="default"
            className="aui-composer-cancel"
          >
            <CircleStopIcon />
          </TooltipIconButton>
        </ComposerPrimitive.Cancel>
      </ThreadPrimitive.If>
    </>
  );
};

const UserMessage: FC = () => {
  return (
    <MessagePrimitive.Root className="aui-user-message-root">
      <UserActionBar />

      <div className="aui-user-message-content">
        <MessagePrimitive.Parts />
      </div>

      <BranchPicker className="aui-user-branch-picker" />
    </MessagePrimitive.Root>
  );
};

const UserActionBar: FC = () => {
  return (
    <ActionBarPrimitive.Root
      hideWhenRunning
      autohide="not-last"
      className="aui-user-action-bar-root"
    >
      <ActionBarPrimitive.Edit asChild>
        <TooltipIconButton tooltip="Edit">
          <PencilIcon />
        </TooltipIconButton>
      </ActionBarPrimitive.Edit>
    </ActionBarPrimitive.Root>
  );
};

const EditComposer: FC = () => {
  return (
    <ComposerPrimitive.Root className="aui-edit-composer-root">
      <ComposerPrimitive.Input className="aui-edit-composer-input" />

      <div className="aui-edit-composer-footer">
        <ComposerPrimitive.Cancel asChild>
          <Button variant="ghost">Cancel</Button>
        </ComposerPrimitive.Cancel>
        <ComposerPrimitive.Send asChild>
          <Button>Send</Button>
        </ComposerPrimitive.Send>
      </div>
    </ComposerPrimitive.Root>
  );
};

const AssistantMessage: FC = () => {
  return (
    <MessagePrimitive.Root className="aui-assistant-message-root">
      <div className="aui-assistant-message-content">
        <MessagePrimitive.Parts components={{ Text: MarkdownText }} />
      </div>

      <AssistantActionBar />

      <BranchPicker className="aui-assistant-branch-picker" />
    </MessagePrimitive.Root>
  );
};

const AssistantActionBar: FC = () => {
  return (
    <ActionBarPrimitive.Root
      hideWhenRunning
      autohide="not-last"
      autohideFloat="single-branch"
      className="aui-assistant-action-bar-root"
    >
      <ActionBarPrimitive.Copy asChild>
        <TooltipIconButton tooltip="Copy">
          <MessagePrimitive.If copied>
            <CheckIcon />
          </MessagePrimitive.If>
          <MessagePrimitive.If copied={false}>
            <CopyIcon />
          </MessagePrimitive.If>
        </TooltipIconButton>
      </ActionBarPrimitive.Copy>
      <ActionBarPrimitive.Reload asChild>
        <TooltipIconButton tooltip="Refresh">
          <RefreshCwIcon />
        </TooltipIconButton>
      </ActionBarPrimitive.Reload>
    </ActionBarPrimitive.Root>
  );
};

const BranchPicker: FC<BranchPickerPrimitive.Root.Props> = ({
  className,
  ...rest
}) => {
  return (
    <BranchPickerPrimitive.Root
      hideWhenSingleBranch
      className={cn("aui-branch-picker-root", className)}
      {...rest}
    >
      <BranchPickerPrimitive.Previous asChild>
        <TooltipIconButton tooltip="Previous">
          <ChevronLeftIcon />
        </TooltipIconButton>
      </BranchPickerPrimitive.Previous>
      <span className="aui-branch-picker-state">
        <BranchPickerPrimitive.Number /> / <BranchPickerPrimitive.Count />
      </span>
      <BranchPickerPrimitive.Next asChild>
        <TooltipIconButton tooltip="Next">
          <ChevronRightIcon />
        </TooltipIconButton>
      </BranchPickerPrimitive.Next>
    </BranchPickerPrimitive.Root>
  );
};

const CircleStopIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 16 16"
      fill="currentColor"
      width="16"
      height="16"
    >
      <rect width="10" height="10" x="3" y="3" rx="2" />
    </svg>
  );
};
