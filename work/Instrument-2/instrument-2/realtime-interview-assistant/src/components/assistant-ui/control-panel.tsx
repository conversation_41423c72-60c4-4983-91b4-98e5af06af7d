"use client";

import { FC, useState } from 'react';
import { 
  MicIcon, 
  MicOffIcon, 
  VolumeXIcon, 
  Volume2Icon, 

  SettingsIcon,
  WifiIcon,
  WifiOffIcon,
  AlertTriangleIcon,
  ClockIcon,
  ActivityIcon
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { TooltipIconButton } from '@/components/assistant-ui/tooltip-icon-button';
import { cn } from '@/lib/utils';
import { ConnectionState } from '@/types/realtime';
import { SessionMetrics, VOICE_OPTIONS, VoiceOption } from '@/lib/session-config';

interface ControlPanelProps {
  // Connection state
  connectionState: ConnectionState;
  isConnected: boolean;
  
  // Audio states
  isRecording: boolean;
  isPlaying: boolean;
  isMuted: boolean;
  playbackRate: number;
  
  // Session info
  sessionMetrics: SessionMetrics;
  isSessionExpired: boolean;
  shouldShowWarning: boolean;
  
  // Controls
  onConnect: () => void;
  onDisconnect: () => void;
  onToggleRecording: () => void;
  onTriggerResponse: () => void;
  onToggleMute: () => void;
  onPlaybackRateChange: (rate: number) => void;
  onVoiceChange?: (voice: VoiceOption) => void;
  
  // Status
  error?: string | null;
}

export const ControlPanel: FC<ControlPanelProps> = ({
  connectionState,
  isConnected,
  isRecording,
  isPlaying,
  isMuted,
  playbackRate,
  sessionMetrics,
  isSessionExpired,
  shouldShowWarning,
  onConnect,
  onDisconnect,
  onToggleRecording,
  onTriggerResponse,
  onToggleMute,
  onPlaybackRateChange,
  onVoiceChange,
  error
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [selectedVoice, setSelectedVoice] = useState<VoiceOption>('alloy');

  const playbackRates = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

  const formatDuration = (ms: number): string => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getConnectionStatusColor = () => {
    switch (connectionState) {
      case ConnectionState.CONNECTED:
        return 'text-green-600';
      case ConnectionState.CONNECTING:
        return 'text-yellow-600';
      case ConnectionState.ERROR:
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionState) {
      case ConnectionState.CONNECTED:
        return 'Connected';
      case ConnectionState.CONNECTING:
        return 'Connecting...';
      case ConnectionState.RECONNECTING:
        return 'Reconnecting...';
      case ConnectionState.ERROR:
        return 'Connection Error';
      default:
        return 'Disconnected';
    }
  };

  const handleVoiceChange = (voice: VoiceOption) => {
    setSelectedVoice(voice);
    onVoiceChange?.(voice);
  };

  return (
    <div className="bg-white border-t border-gray-200 p-4">
      {/* Status Bar */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          {/* Connection Status */}
          <div className="flex items-center gap-2">
            {isConnected ? (
              <WifiIcon className={cn("w-4 h-4", getConnectionStatusColor())} />
            ) : (
              <WifiOffIcon className={cn("w-4 h-4", getConnectionStatusColor())} />
            )}
            <span className={cn("text-sm font-medium", getConnectionStatusColor())}>
              {getConnectionStatusText()}
            </span>
          </div>

          {/* Session Timer */}
          {isConnected && (
            <div className="flex items-center gap-2">
              <ClockIcon className="w-4 h-4 text-gray-500" />
              <span className={cn(
                "text-sm font-mono",
                shouldShowWarning ? "text-orange-600" : "text-gray-600",
                isSessionExpired ? "text-red-600" : ""
              )}>
                {formatDuration(sessionMetrics.duration)}
              </span>
            </div>
          )}

          {/* Activity Indicator */}
          {(isRecording || isPlaying) && (
            <div className="flex items-center gap-2">
              <ActivityIcon className="w-4 h-4 text-blue-500 animate-pulse" />
              <span className="text-sm text-blue-600">
                {isRecording && isPlaying ? 'Recording & Playing' : 
                 isRecording ? 'Recording' : 'Playing'}
              </span>
            </div>
          )}
        </div>

        {/* Warnings */}
        <div className="flex items-center gap-2">
          {shouldShowWarning && (
            <div className="flex items-center gap-1 text-orange-600">
              <AlertTriangleIcon className="w-4 h-4" />
              <span className="text-sm">Session expires soon</span>
            </div>
          )}
          
          {error && (
            <div className="flex items-center gap-1 text-red-600">
              <AlertTriangleIcon className="w-4 h-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}
        </div>
      </div>

      {/* Main Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {/* Connection Control */}
          {!isConnected ? (
            <Button 
              onClick={onConnect}
              disabled={connectionState === ConnectionState.CONNECTING}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {connectionState === ConnectionState.CONNECTING ? 'Connecting...' : 'Connect'}
            </Button>
          ) : (
            <Button 
              onClick={onDisconnect}
              variant="outline"
              className="border-red-300 text-red-600 hover:bg-red-50"
            >
              Disconnect
            </Button>
          )}

          <div className="w-px h-8 bg-gray-300" />

          {/* Recording Control */}
          <TooltipIconButton
            tooltip={isRecording ? "Stop Recording" : "Start Recording"}
            onClick={onToggleRecording}
            disabled={!isConnected}
            className={cn(
              "transition-colors",
              isRecording 
                ? "bg-red-100 text-red-600 hover:bg-red-200" 
                : "hover:bg-gray-100",
              !isConnected && "opacity-50 cursor-not-allowed"
            )}
          >
            {isRecording ? <MicOffIcon /> : <MicIcon />}
          </TooltipIconButton>

          {/* Trigger Response */}
          <Button
            onClick={onTriggerResponse}
            disabled={!isConnected}
            className={cn(
              "bg-blue-600 hover:bg-blue-700 text-white",
              !isConnected && "opacity-50 cursor-not-allowed"
            )}
          >
            Trigger AI Response
          </Button>

          <div className="w-px h-8 bg-gray-300" />

          {/* Audio Controls */}
          <TooltipIconButton
            tooltip={isMuted ? "Unmute" : "Mute"}
            onClick={onToggleMute}
            disabled={!isConnected}
            className={cn(
              "transition-colors",
              isMuted 
                ? "bg-red-100 text-red-600 hover:bg-red-200" 
                : "hover:bg-gray-100",
              !isConnected && "opacity-50 cursor-not-allowed"
            )}
          >
            {isMuted ? <VolumeXIcon /> : <Volume2Icon />}
          </TooltipIconButton>

          {/* Playback Speed */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Speed:</span>
            <select
              value={playbackRate}
              onChange={(e) => onPlaybackRateChange(parseFloat(e.target.value))}
              disabled={!isConnected}
              className={cn(
                "text-sm border rounded px-2 py-1 bg-white min-w-[70px]",
                !isConnected && "opacity-50 cursor-not-allowed"
              )}
            >
              {playbackRates.map(rate => (
                <option key={rate} value={rate}>
                  {rate}x
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Settings */}
        <div className="flex items-center gap-2">
          <TooltipIconButton
            tooltip="Settings"
            onClick={() => setShowSettings(!showSettings)}
            className="hover:bg-gray-100"
          >
            <SettingsIcon />
          </TooltipIconButton>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Settings</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Voice Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                AI Voice
              </label>
              <select
                value={selectedVoice}
                onChange={(e) => handleVoiceChange(e.target.value as VoiceOption)}
                className="w-full text-sm border rounded px-3 py-2 bg-white"
              >
                {VOICE_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Session Stats */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Session Statistics
              </label>
              <div className="text-sm text-gray-600 space-y-1">
                <div>Messages: {sessionMetrics.messagesExchanged}</div>
                <div>Audio chunks: {sessionMetrics.audioChunksProcessed}</div>
                <div>Reconnections: {sessionMetrics.reconnectionCount}</div>
              </div>
            </div>
          </div>

          {/* Keyboard Shortcuts */}
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Keyboard Shortcuts</h4>
            <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
              <div><kbd className="bg-gray-200 px-1 rounded">Space</kbd> Trigger Response</div>
              <div><kbd className="bg-gray-200 px-1 rounded">M</kbd> Toggle Mute</div>
              <div><kbd className="bg-gray-200 px-1 rounded">R</kbd> Toggle Recording</div>
              <div><kbd className="bg-gray-200 px-1 rounded">Esc</kbd> Disconnect</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
