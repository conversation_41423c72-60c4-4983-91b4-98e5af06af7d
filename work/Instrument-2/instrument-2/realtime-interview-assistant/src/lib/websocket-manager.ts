import { 
  RealtimeEvent, 
  RealtimeSession, 
  ConnectionState, 
  SessionManager,
  ConversationItemCreateEvent,
  SessionUpdateEvent,
  InputAudioBufferAppendEvent,
  InputAudioBufferCommitEvent,
  InputAudioBufferClearEvent,
  ResponseCreateEvent,
  ResponseCancelEvent
} from '@/types/realtime';

export class RealtimeWebSocketManager {
  private ws: WebSocket | null = null;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private apiKey: string;
  private sessionManager: SessionManager;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventListeners: Map<string, Set<(event: RealtimeEvent) => void>> = new Map();
  private pingInterval: NodeJS.Timeout | null = null;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.sessionManager = {
      sessionId: this.generateSessionId(),
      startTime: Date.now(),
      maxDuration: 30 * 60 * 1000, // 30 minutes
      conversationHistory: []
    };
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public async connect(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTED || 
        this.connectionState === ConnectionState.CONNECTING) {
      return;
    }

    this.setConnectionState(ConnectionState.CONNECTING);

    try {
      const wsUrl = `wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01`;

      // Note: Browser WebSocket API doesn't support headers in constructor
      // Authentication will need to be handled via URL parameters or after connection
      this.ws = new WebSocket(wsUrl);

      this.setupWebSocketEventHandlers();
      
      // Wait for connection to be established
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.ws!.onopen = () => {
          clearTimeout(timeout);
          this.setConnectionState(ConnectionState.CONNECTED);
          this.reconnectAttempts = 0;
          this.startPingInterval();
          resolve();
        };

        this.ws!.onerror = (error) => {
          clearTimeout(timeout);
          reject(error);
        };
      });

    } catch (error) {
      this.setConnectionState(ConnectionState.ERROR);
      throw error;
    }
  }

  private setupWebSocketEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onmessage = (event) => {
      try {
        const data: RealtimeEvent = JSON.parse(event.data);
        this.handleServerEvent(data);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = (event) => {
      this.setConnectionState(ConnectionState.DISCONNECTED);
      this.stopPingInterval();
      
      if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.setConnectionState(ConnectionState.ERROR);
    };
  }

  private handleServerEvent(event: RealtimeEvent): void {
    // Emit to registered listeners
    const listeners = this.eventListeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => listener(event));
    }

    // Emit to general listeners
    const generalListeners = this.eventListeners.get('*');
    if (generalListeners) {
      generalListeners.forEach(listener => listener(event));
    }
  }

  private scheduleReconnect(): void {
    this.setConnectionState(ConnectionState.RECONNECTING);
    this.reconnectAttempts++;
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          this.setConnectionState(ConnectionState.ERROR);
        }
      });
    }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1));
  }

  private startPingInterval(): void {
    this.pingInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.sendEvent({ type: 'ping' });
      }
    }, 30000); // Ping every 30 seconds
  }

  private stopPingInterval(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  public sendEvent(event: RealtimeEvent): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(event));
    } else {
      console.warn('WebSocket not connected, cannot send event:', event);
    }
  }

  public updateSession(session: Partial<RealtimeSession>): void {
    const event: SessionUpdateEvent = {
      type: 'session.update',
      session
    };
    this.sendEvent(event);
  }

  public appendAudioBuffer(audioData: string): void {
    const event: InputAudioBufferAppendEvent = {
      type: 'input_audio_buffer.append',
      audio: audioData
    };
    this.sendEvent(event);
  }

  public commitAudioBuffer(): void {
    const event: InputAudioBufferCommitEvent = {
      type: 'input_audio_buffer.commit'
    };
    this.sendEvent(event);
  }

  public clearAudioBuffer(): void {
    const event: InputAudioBufferClearEvent = {
      type: 'input_audio_buffer.clear'
    };
    this.sendEvent(event);
  }

  public createResponse(instructions?: string): void {
    const event: ResponseCreateEvent = {
      type: 'response.create',
      response: instructions ? { instructions } : undefined
    };
    this.sendEvent(event);
  }

  public cancelResponse(): void {
    const event: ResponseCancelEvent = {
      type: 'response.cancel'
    };
    this.sendEvent(event);
  }

  public addEventListener(eventType: string, listener: (event: RealtimeEvent) => void): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    this.eventListeners.get(eventType)!.add(listener);
  }

  public removeEventListener(eventType: string, listener: (event: RealtimeEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  private setConnectionState(state: ConnectionState): void {
    this.connectionState = state;
    this.handleServerEvent({ type: 'connection.state_changed', state });
  }

  public getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  public isSessionExpired(): boolean {
    return Date.now() - this.sessionManager.startTime > this.sessionManager.maxDuration;
  }

  public async reconnectWithHistory(): Promise<void> {
    await this.connect();
    
    // Restore conversation history
    for (const item of this.sessionManager.conversationHistory) {
      this.sendEvent(item);
    }
  }

  public addToConversationHistory(item: ConversationItemCreateEvent): void {
    this.sessionManager.conversationHistory.push(item);
  }

  public disconnect(): void {
    this.stopPingInterval();
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.setConnectionState(ConnectionState.DISCONNECTED);
  }
}
