import { AudioConfig } from '@/types/realtime';

export class AudioInputManager {
  private mediaStream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private processor: ScriptProcessorNode | null = null;
  private source: MediaStreamAudioSourceNode | null = null;
  private isRecording = false;
  private onAudioData: ((audioData: string) => void) | null = null;
  private audioConfig: AudioConfig = {
    sampleRate: 24000,
    channels: 1,
    bitsPerSample: 16
  };

  constructor() {
    // Bind methods to preserve context
    this.processAudio = this.processAudio.bind(this);
  }

  public async initialize(): Promise<void> {
    try {
      // Request microphone access with echo cancellation
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: this.audioConfig.sampleRate,
          channelCount: this.audioConfig.channels
        }
      });

      // Create audio context
      this.audioContext = new (window.AudioContext || (window as unknown as { webkitAudioContext: typeof AudioContext }).webkitAudioContext)({
        sampleRate: this.audioConfig.sampleRate
      });

      // Create source from media stream
      this.source = this.audioContext.createMediaStreamSource(this.mediaStream);

      // Create script processor for audio processing
      // Note: ScriptProcessorNode is deprecated but still widely supported
      // In production, consider migrating to AudioWorklet
      this.processor = this.audioContext.createScriptProcessor(4096, 1, 1);
      this.processor.onaudioprocess = this.processAudio;

      console.log('Audio input initialized successfully');
    } catch (error) {
      console.error('Failed to initialize audio input:', error);
      throw new Error('Microphone access denied or not available');
    }
  }

  public startRecording(onAudioData: (audioData: string) => void): void {
    if (!this.audioContext || !this.source || !this.processor) {
      throw new Error('Audio input not initialized');
    }

    if (this.isRecording) {
      console.warn('Recording already in progress');
      return;
    }

    this.onAudioData = onAudioData;
    this.isRecording = true;

    // Connect the audio processing chain
    this.source.connect(this.processor);
    this.processor.connect(this.audioContext.destination);

    // Resume audio context if suspended
    if (this.audioContext.state === 'suspended') {
      this.audioContext.resume();
    }

    console.log('Audio recording started');
  }

  public stopRecording(): void {
    if (!this.isRecording) {
      return;
    }

    this.isRecording = false;
    this.onAudioData = null;

    // Disconnect the audio processing chain
    if (this.source && this.processor) {
      this.source.disconnect();
      this.processor.disconnect();
    }

    console.log('Audio recording stopped');
  }

  private processAudio(event: AudioProcessingEvent): void {
    if (!this.isRecording || !this.onAudioData) {
      return;
    }

    const inputBuffer = event.inputBuffer;
    const inputData = inputBuffer.getChannelData(0);

    // Convert Float32Array to Int16Array (PCM 16-bit)
    const pcmData = this.float32ToInt16(inputData);

    // Convert to base64 for transmission
    const base64Audio = this.arrayBufferToBase64(pcmData.buffer as ArrayBuffer);

    // Send audio data to callback
    this.onAudioData(base64Audio);
  }

  private float32ToInt16(float32Array: Float32Array): Int16Array {
    const int16Array = new Int16Array(float32Array.length);
    
    for (let i = 0; i < float32Array.length; i++) {
      // Clamp values to [-1, 1] range
      const clamped = Math.max(-1, Math.min(1, float32Array[i]));
      // Convert to 16-bit integer
      int16Array[i] = clamped * 0x7FFF;
    }
    
    return int16Array;
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    
    return btoa(binary);
  }

  public getAudioConfig(): AudioConfig {
    return { ...this.audioConfig };
  }

  public isInitialized(): boolean {
    return this.audioContext !== null && this.mediaStream !== null;
  }

  public isActivelyRecording(): boolean {
    return this.isRecording;
  }

  public async getAudioDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'audioinput');
    } catch (error) {
      console.error('Failed to enumerate audio devices:', error);
      return [];
    }
  }

  public async switchAudioDevice(deviceId: string): Promise<void> {
    if (this.isRecording) {
      throw new Error('Cannot switch device while recording');
    }

    // Stop current stream
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
    }

    // Request new stream with specific device
    this.mediaStream = await navigator.mediaDevices.getUserMedia({
      audio: {
        deviceId: { exact: deviceId },
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: this.audioConfig.sampleRate,
        channelCount: this.audioConfig.channels
      }
    });

    // Recreate source
    if (this.audioContext) {
      this.source = this.audioContext.createMediaStreamSource(this.mediaStream);
    }
  }

  public getAudioLevel(): number {
    if (!this.audioContext || !this.source) {
      return 0;
    }

    // This is a simplified audio level detection
    // In a production app, you might want to use AnalyserNode for more accurate level detection
    return this.isRecording ? 0.5 : 0;
  }

  public cleanup(): void {
    this.stopRecording();

    if (this.processor) {
      this.processor.disconnect();
      this.processor = null;
    }

    if (this.source) {
      this.source.disconnect();
      this.source = null;
    }

    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }

    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }

    console.log('Audio input cleaned up');
  }
}
