import { RealtimeSession } from '@/types/realtime';

export const INTERVIEW_SYSTEM_PROMPT = `You are an expert system design interviewer with deep knowledge of distributed systems, scalability, and software architecture. Your role is to conduct mock system design interviews that help candidates practice for real technical interviews.

## Your Behavior:
- Listen carefully to the entire conversation between the interviewee and any human interviewer present
- Only respond when explicitly triggered (when the user says "AI, your turn" or similar phrases, or when manually triggered)
- Provide insightful questions, clarifications, and feedback based on what you've heard
- Act as a senior engineer who is evaluating the candidate's system design skills

## Interview Focus Areas:
- System architecture and component design
- Scalability and performance considerations
- Data storage and database design
- API design and communication patterns
- Reliability, availability, and fault tolerance
- Security and privacy considerations
- Monitoring and observability
- Trade-offs and decision-making process

## Your Response Style:
- Ask probing questions that reveal deeper understanding
- Point out potential issues or areas for improvement
- Suggest alternative approaches when appropriate
- Provide constructive feedback on the design decisions
- Help guide the conversation toward important architectural considerations
- Be encouraging but maintain technical rigor

## Example Interactions:
- "That's an interesting approach to handling user sessions. How would you ensure session consistency across multiple data centers?"
- "I notice you mentioned using a cache. Can you walk me through your cache invalidation strategy?"
- "What happens to your system if the message queue becomes unavailable?"
- "How would you monitor the health of this distributed system?"

## Important Guidelines:
- Never interrupt natural conversation flow
- Wait for explicit triggers before responding
- Keep responses concise but technically substantive
- Focus on system design principles, not implementation details
- Encourage the candidate to think through trade-offs
- Maintain a professional, supportive tone

Remember: You are here to help the candidate practice and improve their system design interview skills through thoughtful questions and feedback.`;

export const DEFAULT_SESSION_CONFIG: Partial<RealtimeSession> = {
  modalities: ['text', 'audio'],
  voice: 'alloy',
  instructions: INTERVIEW_SYSTEM_PROMPT,
  turn_detection: null, // Disable server-side VAD for manual control
  input_audio_transcription: {
    model: 'whisper-1'
  },
  tools: []
};

export const VOICE_OPTIONS = [
  { value: 'alloy', label: 'Alloy (Neutral)' },
  { value: 'echo', label: 'Echo (Male)' },
  { value: 'fable', label: 'Fable (British Male)' },
  { value: 'onyx', label: 'Onyx (Deep Male)' },
  { value: 'nova', label: 'Nova (Female)' },
  { value: 'shimmer', label: 'Shimmer (Soft Female)' }
] as const;

export type VoiceOption = typeof VOICE_OPTIONS[number]['value'];

export interface SessionConfigOptions {
  voice?: VoiceOption;
  customInstructions?: string;
  enableTranscription?: boolean;
  interviewType?: 'system-design' | 'coding' | 'behavioral';
}

export function createSessionConfig(options: SessionConfigOptions = {}): Partial<RealtimeSession> {
  const {
    voice = 'alloy',
    customInstructions,
    enableTranscription = true,
    interviewType = 'system-design'
  } = options;

  let instructions = INTERVIEW_SYSTEM_PROMPT;

  // Customize instructions based on interview type
  if (interviewType === 'coding') {
    instructions = instructions.replace(
      'system design interviewer',
      'coding interviewer focusing on algorithms and data structures'
    ).replace(
      'system design skills',
      'coding and problem-solving skills'
    );
  } else if (interviewType === 'behavioral') {
    instructions = instructions.replace(
      'system design interviewer',
      'behavioral interviewer focusing on leadership and communication'
    ).replace(
      'system design skills',
      'behavioral and leadership skills'
    );
  }

  // Append custom instructions if provided
  if (customInstructions) {
    instructions += `\n\nAdditional Instructions:\n${customInstructions}`;
  }

  return {
    ...DEFAULT_SESSION_CONFIG,
    voice,
    instructions,
    input_audio_transcription: enableTranscription ? {
      model: 'whisper-1'
    } : undefined
  };
}

export const TRIGGER_PHRASES = [
  'AI, your turn',
  'AI feedback',
  'What do you think?',
  'AI, please respond',
  'Your thoughts?',
  'AI input',
  'Interviewer feedback'
];

export function detectTriggerPhrase(text: string): boolean {
  const lowerText = text.toLowerCase();
  return TRIGGER_PHRASES.some(phrase => 
    lowerText.includes(phrase.toLowerCase())
  );
}

export const SESSION_LIMITS = {
  MAX_DURATION_MS: 30 * 60 * 1000, // 30 minutes
  WARNING_THRESHOLD_MS: 25 * 60 * 1000, // 25 minutes
  RECONNECT_THRESHOLD_MS: 28 * 60 * 1000, // 28 minutes
  MAX_CONVERSATION_ITEMS: 100, // Limit conversation history
  AUDIO_CHUNK_SIZE: 4096, // Audio processing chunk size
  MAX_AUDIO_BUFFER_MS: 10000 // 10 seconds max buffer
} as const;

export interface SessionMetrics {
  startTime: number;
  duration: number;
  messagesExchanged: number;
  audioChunksProcessed: number;
  reconnectionCount: number;
  lastActivity: number;
}

export function createSessionMetrics(): SessionMetrics {
  return {
    startTime: Date.now(),
    duration: 0,
    messagesExchanged: 0,
    audioChunksProcessed: 0,
    reconnectionCount: 0,
    lastActivity: Date.now()
  };
}

export function updateSessionMetrics(
  metrics: SessionMetrics,
  updates: Partial<SessionMetrics>
): SessionMetrics {
  return {
    ...metrics,
    ...updates,
    duration: Date.now() - metrics.startTime,
    lastActivity: Date.now()
  };
}

export function isSessionExpired(metrics: SessionMetrics): boolean {
  return metrics.duration >= SESSION_LIMITS.MAX_DURATION_MS;
}

export function shouldShowWarning(metrics: SessionMetrics): boolean {
  return metrics.duration >= SESSION_LIMITS.WARNING_THRESHOLD_MS && 
         metrics.duration < SESSION_LIMITS.MAX_DURATION_MS;
}

export function shouldReconnect(metrics: SessionMetrics): boolean {
  return metrics.duration >= SESSION_LIMITS.RECONNECT_THRESHOLD_MS && 
         metrics.duration < SESSION_LIMITS.MAX_DURATION_MS;
}
