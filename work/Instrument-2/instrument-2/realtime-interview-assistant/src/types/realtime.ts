// OpenAI Realtime API Types
export interface RealtimeSession {
  modalities: string[];
  voice: string;
  instructions: string;
  turn_detection: null | {
    type: string;
    threshold: number;
    prefix_padding_ms: number;
    silence_duration_ms: number;
  };
  input_audio_transcription?: {
    model: string;
  };
  tools?: unknown[];
}

export interface RealtimeEvent {
  type: string;
  event_id?: string;
  [key: string]: unknown;
}

// Session Events
export interface SessionUpdateEvent extends RealtimeEvent {
  type: 'session.update';
  session: Partial<RealtimeSession>;
}

// Input Audio Buffer Events
export interface InputAudioBufferAppendEvent extends RealtimeEvent {
  type: 'input_audio_buffer.append';
  audio: string; // base64 encoded audio
}

export interface InputAudioBufferCommitEvent extends RealtimeEvent {
  type: 'input_audio_buffer.commit';
}

export interface InputAudioBufferClearEvent extends RealtimeEvent {
  type: 'input_audio_buffer.clear';
}

// Response Events
export interface ResponseCreateEvent extends RealtimeEvent {
  type: 'response.create';
  response?: {
    modalities?: string[];
    instructions?: string;
  };
}

export interface ResponseCancelEvent extends RealtimeEvent {
  type: 'response.cancel';
}

// Server Events
export interface ResponseAudioDeltaEvent extends RealtimeEvent {
  type: 'response.audio.delta';
  response_id: string;
  item_id: string;
  output_index: number;
  content_index: number;
  delta: string; // base64 encoded audio
}

export interface ResponseAudioTranscriptDeltaEvent extends RealtimeEvent {
  type: 'response.audio_transcript.delta';
  response_id: string;
  item_id: string;
  output_index: number;
  content_index: number;
  delta: string;
}

export interface InputAudioBufferSpeechStartedEvent extends RealtimeEvent {
  type: 'input_audio_buffer.speech_started';
  audio_start_ms: number;
  item_id: string;
}

export interface InputAudioBufferSpeechStoppedEvent extends RealtimeEvent {
  type: 'input_audio_buffer.speech_stopped';
  audio_end_ms: number;
  item_id: string;
}

export interface ConversationItemCreateEvent extends RealtimeEvent {
  type: 'conversation.item.create';
  item: {
    id?: string;
    type: 'message';
    role: 'user' | 'assistant';
    content: Array<{
      type: 'input_text' | 'input_audio' | 'text' | 'audio';
      text?: string;
      audio?: string;
      transcript?: string;
    }>;
  };
}

// WebSocket Connection States
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// Audio Configuration
export interface AudioConfig {
  sampleRate: number;
  channels: number;
  bitsPerSample: number;
}

// Session Management
export interface SessionManager {
  sessionId: string;
  startTime: number;
  maxDuration: number;
  conversationHistory: ConversationItemCreateEvent[];
}
