import { NextRequest } from 'next/server';
import WebSocket from 'ws';

export async function GET(request: NextRequest) {
  const apiKey = request.headers.get('authorization')?.replace('Bearer ', '');
  
  if (!apiKey) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Check if this is a WebSocket upgrade request
  const upgrade = request.headers.get('upgrade');
  if (upgrade !== 'websocket') {
    return new Response('Expected WebSocket upgrade', { status: 400 });
  }

  try {
    // Create WebSocket connection to OpenAI Realtime API
    const openaiWs = new WebSocket('wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'OpenAI-Beta': 'realtime=v1'
      }
    });

    // Handle the WebSocket upgrade for the client
    const { socket, response } = await upgradeWebSocket(request);

    // Proxy messages between client and OpenAI
    socket.on('message', (data) => {
      if (openaiWs.readyState === WebSocket.OPEN) {
        openaiWs.send(data);
      }
    });

    openaiWs.on('message', (data) => {
      if (socket.readyState === WebSocket.OPEN) {
        socket.send(data);
      }
    });

    // Handle connection events
    openaiWs.on('open', () => {
      console.log('Connected to OpenAI Realtime API');
    });

    openaiWs.on('error', (error) => {
      console.error('OpenAI WebSocket error:', error);
      socket.close(1011, 'OpenAI connection error');
    });

    openaiWs.on('close', () => {
      socket.close();
    });

    socket.on('close', () => {
      openaiWs.close();
    });

    return response;

  } catch (error) {
    console.error('WebSocket proxy error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

// Helper function to upgrade HTTP to WebSocket (simplified for Next.js)
async function upgradeWebSocket(request: NextRequest) {
  // Note: This is a simplified implementation
  // In a real Next.js app, you'd need to use a custom server or different approach
  // For now, we'll return a mock response
  throw new Error('WebSocket upgrade not implemented in this environment');
}
